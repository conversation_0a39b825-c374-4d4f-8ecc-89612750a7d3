# Tyl Payment Gateway Integration

This document describes the implementation of Tyl payment gateway integration for the Sofa Deal e-commerce platform.

## Overview

The payment integration follows a hosted payment page model where customers are redirected to NatWest's secure payment forms, ensuring PCI compliance and security.

## Implementation Files

### Core Payment Files

- `src/lib/api/payment.ts` - Payment API service and TypeScript interfaces
- `src/lib/utils/payment-utils.ts` - Payment utility functions
- `src/app/payment/success/page.tsx` - Payment success page
- `src/app/payment/failure/page.tsx` - Payment failure page

### Updated Files

- `src/app/cart/page.tsx` - Updated checkout flow with real payment integration
- `src/lib/api-service.ts` - Fixed API URL configuration

## Payment Flow

1. **Customer initiates checkout** - Fills out contact and shipping information
2. **Frontend stores order data** - Saves cart items and order details to localStorage
3. **Frontend calls `/orders/create-payment`** - Sends customer and cart data to backend
4. **Backend creates order & generates Tyl payment form** - Returns form data for auto-submission
5. **Frontend auto-submits form** - Immediately redirects customer to NatWest hosted payment page
6. **Customer completes payment** - On NatWest's secure hosted page
7. **NatWest redirects back** - To success/failure URLs based on payment outcome
8. **Success page displays dynamic order data** - Shows real cart items and order details
9. **Backend receives webhook** - NatWest sends notification to update order status

## Key Features

### Payment API Service (`PaymentApiService`)

- `createPayment()` - Creates payment order and gets Tyl form data
- `validatePaymentData()` - Client-side validation before API call
- Comprehensive error handling for different failure scenarios

### Payment Utilities

- `redirectToPayment()` - Auto-submits payment form to NatWest (no delays)
- `getPaymentErrorMessage()` - User-friendly error messages
- `storeOrderId()` / `getStoredOrderId()` / `clearStoredOrderId()` - Order ID management
- `convertCartItemsToPaymentFormat()` - Cart data transformation
- Address and phone number formatting utilities
- Order data persistence in localStorage for dynamic success page

### Payment Pages

#### Success Page (`/payment/success`)

- **Dynamic order display** - Shows real cart items with images and quantities
- **Real order data** - Displays actual order code, date, total, and payment method
- **Persistent data** - Uses localStorage to maintain order info across page refreshes
- **Fallback handling** - Shows error if no order data available
- **Cart integration** - Retrieves data from cart store and localStorage

#### Failure Page (`/payment/failure`)

- **Error handling** - Displays payment failure information
- **Retry options** - Allows customer to return to cart and retry payment

## Current Implementation Status

### ✅ Completed Features

1. **Payment Integration**
   - ✅ NatWest Tyl payment gateway integration
   - ✅ Hosted payment page implementation
   - ✅ Auto-form submission to payment gateway
   - ✅ No delays - immediate redirect to payment

2. **Dynamic Success Page**
   - ✅ Real cart items display with images and quantities
   - ✅ Actual order data (order code, date, total, payment method)
   - ✅ localStorage persistence for page refreshes
   - ✅ Fallback error handling for missing data

3. **Order Data Management**
   - ✅ Order data storage before payment redirect
   - ✅ Order ID management and cleanup
   - ✅ Cart data transformation for payment API

4. **Error Handling**
   - ✅ Comprehensive payment error messages
   - ✅ API validation and error responses
   - ✅ User-friendly error display

### 🔄 Current Behavior

- **Cart Page**: Stores order data → Redirects immediately to NatWest payment
- **Payment**: Customer completes payment on NatWest hosted page
- **Success Page**: Displays real order data from localStorage + cart store
- **Navigation**: Clean navigation back to home or continue shopping

### Enhanced Checkout

- Real-time validation of form data
- Loading states during payment processing
- Comprehensive error handling and user feedback
- Integration with existing cart store

## Configuration

### Environment Variables

The following environment variables should be configured:

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000

# Backend will handle Tyl credentials:
# TYL_STORE_NAME=7220542049
# TYL_USERNAME=7220542049
# TYL_PASSWORD=uEQy5$Ex<2NP
# TYL_SHARED_SECRET=Mz8v'kA5<Aug
# TYL_BASE_URL=https://test.ipg-online.com/vt/login/natwest_tyl
# TYL_PAYMENT_URL=https://test.ipg-online.com/connect/gateway/processing
```

### URL Configuration

- Success URL: `${FRONTEND_BASE_URL}/payment/success`
- Failure URL: `${FRONTEND_BASE_URL}/payment/failure`
- Webhook URL: `${BACKEND_BASE_URL}/orders/payment/webhook`

**Note:** The backend handles payment gateway callbacks and redirects directly to the success/failure pages with URL parameters.

## Testing

### Test Credentials

#### NatWest Tyl Test Environment

- **Test Portal:** https://test.ipg-online.com/vt/login/natwest_tyl
- **Store Name:** 7220542049
- **Username:** 7220542049
- **Password:** uEQy5$Ex<2NP
- **Shared Secret:** Mz8v'kA5<Aug

#### Test Card Details

For testing payments, use these **official NatWest Tyl test card numbers**:

**✅ Successful Payments:**

- **Visa:** 4147 4630 1111 0083
- **MasterCard:** 5239 2907 0000 0028
- **Expiry:** Any future date (e.g., 12/25)
- **CVV:** Any 3 digits (e.g., 123)
- **Cardholder Name:** Any name

**❌ Test Declines:**

- **Visa:** 4147 4630 1111 0042
- **Expiry:** Any future date
- **CVV:** Any 3 digits

**📋 Additional Test Cards:**

- 4012 0000 0001 2011 004
- 5204 7400 0000 2711
- 4265 8800 0000 0015
- 5426 0640 0042 5117

### Test Scenarios

#### 1. **Successful Payment Flow**

- Add items to cart
- Fill out checkout form with valid details
- Use test card: `4147 4630 1111 0083`
- **Expected**: Immediate redirect to NatWest → Payment success → Dynamic success page with real order data

#### 2. **Success Page Persistence**

- Complete successful payment
- Refresh the success page
- **Expected**: Order data still displays correctly (localStorage persistence)

#### 3. **Failed Payment**

- Use invalid card: `4147 4630 1111 0042`
- **Expected**: Redirect to failure page with retry options

#### 4. **Empty Cart Handling**

- Navigate to success page without completing payment
- **Expected**: Error message about missing order data

#### 5. **Validation Errors**

- Submit incomplete checkout form
- **Expected**: Client-side validation errors before payment redirect

#### 6. **Dynamic Order Display**

- Add multiple items with different quantities
- Complete payment
- **Expected**: Success page shows all items with correct images, quantities, and total

## Error Handling

The implementation handles various error scenarios:

### Validation Errors (400)

- Missing required fields
- Invalid email format
- Empty cart

### Stock Issues (409)

- Insufficient inventory
- Out-of-stock items

### Not Found Errors (404)

- Invalid variant IDs
- Product not found

### Server Errors (500)

- Internal server errors
- Payment gateway issues

## Security Considerations

1. **PCI Compliance** - No card details stored locally
2. **HTTPS Required** - All payment pages use secure connections
3. **Data Validation** - Comprehensive client and server-side validation
4. **Error Logging** - Secure logging without sensitive data
5. **Token Management** - Automatic token refresh for authenticated requests

## Usage Example

```typescript
import { PaymentApiService, CreatePaymentRequest } from "@/lib/api/payment";
import { redirectToPayment } from "@/lib/utils/payment-utils";

// Prepare payment data
const paymentData: CreatePaymentRequest = {
  contact_first_name: "John",
  contact_last_name: "Doe",
  contact_email: "<EMAIL>",
  shipping_address: {
    street_address: "123 Main St",
    city: "London",
    postal_code: "SW1A 1AA",
    country: "GB",
    country_name: "United Kingdom",
  },
  use_different_billing_address: false,
  cart_items: [
    {
      variant_id: "uuid-here",
      quantity: 1,
    },
  ],
};

// Create payment and redirect
try {
  const response = await PaymentApiService.createPayment(paymentData);
  redirectToPayment(response);
} catch (error) {
  console.error("Payment failed:", error);
}
```

## Next Steps

1. **Backend Integration** - Ensure backend implements the `/orders/create-payment` endpoint
2. **Webhook Implementation** - Set up payment webhook handler in backend
3. **Order Management** - Implement order status tracking and management
4. **Email Notifications** - Add order confirmation emails
5. **Testing** - Comprehensive testing with real payment scenarios
6. **Production Setup** - Configure production Tyl credentials and URLs

## Support

For issues or questions about the payment integration:

- Check the API documentation in `docs/APIs/payment-integration.md`
- Review error logs for debugging information
- Contact the development team for technical support
