"use client";

import React, { useState } from "react";
import { Star, Loader2 } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { useCart } from "@/lib/store/cart-store";
import { WishlistButton } from "@/components/wishlist/wishlist-button";
import { toast } from "sonner";

interface ProductCardProps {
  id: string | number;
  name: string;
  price: number;
  originalPrice?: number;
  imageSrc?: string;
  rating?: number;
  ratingCount?: number;
  discount?: string;
  isNew?: boolean;
  isSale?: boolean;
  deliveryInfo?: string;
  paymentOption?: {
    service: string;
    installments: number;
    amount: number;
  };
  variant?: "layout1" | "layout2";
  className?: string;
  // Add variant information for proper cart/wishlist functionality
  variantId?: string;
  size?: string;
  color?: string;
  stock?: number;
}

export function ProductCard({
  id,
  name,
  price,
  originalPrice,
  imageSrc = "/placeholder.svg",
  rating = 4.9,
  discount,
  deliveryInfo,
  paymentOption,
  variant = "layout1",
  className = "",
  variantId,
  size,
  color,
  stock,
}: ProductCardProps) {
  const { addItem } = useCart();
  const [isAddingToCart, setIsAddingToCart] = useState(false);

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Use variantId if provided, otherwise fall back to id
    const effectiveVariantId = variantId || id.toString();

    setIsAddingToCart(true);
    try {
      await addItem({
        id: effectiveVariantId,
        name,
        price,
        image: imageSrc,
        variant_id: effectiveVariantId,
        size,
        color,
        stock,
      });

      toast.success(`${name} added to cart!`);
    } catch (error) {
      console.error("Failed to add to cart:", error);
      toast.error("Failed to add to cart");
    } finally {
      setIsAddingToCart(false);
    }
  };

  const formatPrice = (amount: number) => {
    return `${amount.toFixed(2)}`;
  };

  // Mobile Layout Component
  const MobileLayout = () => (
    <div className="space-y-3">
      {/* 1. Product Name (Title) */}
      <Link href={`/products/${id}`}>
        <h3 className="font-bebas text-dark-gray hover:text-blue overflow-hidden text-lg leading-tight text-ellipsis whitespace-nowrap uppercase transition-colors">
          {name}
        </h3>
      </Link>
      {/* 2. Prices */}
      <div className="space-y-1">
        <div className="flex items-baseline gap-2">
          <span className="text-xl font-bold text-[#222222]">
            ${formatPrice(price)}
          </span>
          {originalPrice && originalPrice > price && (
            <span className="text-base text-gray-500 line-through">
              {formatPrice(originalPrice)}
            </span>
          )}
        </div>
        {deliveryInfo && (
          <div className="text-xs text-gray-600">{deliveryInfo}</div>
        )}
      </div>

      {/* 3. Klarna Payment Option (always after prices) */}
      {paymentOption && (
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <span
              className="inline-block rounded px-2 py-1 text-xs font-bold"
              style={{ backgroundColor: "#FFA8CD", color: "#222222" }}
            >
              {paymentOption.service}
            </span>
          </div>
          <div className="text-xs text-gray-600">
            Make {paymentOption.installments} payments of{" "}
            {formatPrice(paymentOption.amount)}
          </div>
        </div>
      )}

      {/* 4. Add to Cart Button */}
      <button
        onClick={handleAddToCart}
        disabled={isAddingToCart}
        className="bg-blue hover:bg-blue/90 font-open-sans flex h-[35px] w-full items-center justify-center rounded-lg font-medium text-white transition-colors disabled:cursor-not-allowed disabled:opacity-50"
      >
        {isAddingToCart ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Adding...
          </>
        ) : (
          "Add To Cart"
        )}
      </button>
    </div>
  );

  // Desktop Layout Components (original)
  const Layout1Content = () => (
    <>
      {paymentOption && (
        <div className="flex items-center gap-5">
          <span
            className="inline-block rounded-[48.28px] px-7 py-2.5 text-[14px] font-bold"
            style={{ backgroundColor: "#FFA8CD", color: "var(--dark-gray)" }}
          >
            {paymentOption.service}
          </span>
          <div>
            <span className="text-gray text-[20px]">
              Make {paymentOption.installments} Payments Of <br />
              {formatPrice(paymentOption.amount)}
            </span>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <div className="flex items-baseline gap-2">
          <span className="text-[20px] text-[#999999]">
            {formatPrice(price)}
          </span>
          {originalPrice && originalPrice > price && (
            <span className="text-sm text-gray-500 line-through">
              {formatPrice(originalPrice)}
            </span>
          )}
        </div>
        {deliveryInfo && (
          <span className="inline-block rounded-xl bg-[#56748e] px-4 py-2 text-xs font-medium text-white">
            {deliveryInfo}
          </span>
        )}
      </div>
    </>
  );

  const Layout2Content = () => (
    <>
      <div className="flex items-center justify-between">
        <div className="flex items-baseline gap-2">
          <span className="text-[20px] text-[#999999]">
            {formatPrice(price)}
          </span>
          {originalPrice && originalPrice > price && (
            <span className="text-sm text-gray-500 line-through">
              {formatPrice(originalPrice)}
            </span>
          )}
        </div>
        {deliveryInfo && (
          <span className="inline-block rounded-xl bg-[#56748e] px-4 py-2 text-xs font-medium text-white">
            {deliveryInfo}
          </span>
        )}
      </div>

      {paymentOption && (
        <div className="flex items-center gap-5">
          <span
            className="inline-block rounded-[48.28px] px-7 py-2.5 text-[14px] font-bold"
            style={{ backgroundColor: "#FFA8CD", color: "#222222" }}
          >
            {paymentOption.service}
          </span>
          <div>
            <span className="text-[20px] text-[#999999]">
              Make {paymentOption.installments} Payments Of <br />
              {formatPrice(paymentOption.amount)}
            </span>
          </div>
        </div>
      )}
    </>
  );

  const DesktopLayout = () => (
    <div className="space-y-3">
      {/* Product Name and Rating Row */}
      <div className="flex items-center justify-between">
        <Link href={`/products/${id}`}>
          <h3 className="font-bebas text-dark-gray hover:text-blue text-xl uppercase transition-colors md:text-[34px] text-ellipsis">
            {name}
          </h3>
        </Link>
        <div className="flex items-center gap-1">
          <span className="text-sm text-gray-600">{rating}</span>
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
        </div>
      </div>

      {/* Render layout based on variant */}
      {variant === "layout1" ? <Layout1Content /> : <Layout2Content />}

      {/* Add to Cart Button */}
      <button
        onClick={handleAddToCart}
        disabled={isAddingToCart}
        className="group border-blue text-blue font-open-sans hover:text-dark-gray flex h-[50px] w-full items-center justify-between rounded-full border px-2 pl-4 font-medium transition-colors disabled:cursor-not-allowed disabled:opacity-50"
      >
        <span>{isAddingToCart ? "Adding..." : "Add To Cart"}</span>
        <div className="bg-blue group-hover:text-blue group-hover:bg-dark-gray flex h-10 w-10 items-center justify-center rounded-full text-white transition-colors">
          {isAddingToCart ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Image
              src="/farrow-r.png"
              alt="Arrow Right"
              width={35}
              height={35}
              className="h-[35px] w-[35px]"
            />
          )}
        </div>
      </button>
    </div>
  );

  return (
    <div className={`overflow-hidden bg-transparent ${className}`}>
      <Link href={`/products/${id}`} className="block">
        {/* Product Image */}
        <div
          className={`group relative mb-3 ${
            className.includes("list-detailed-view") ||
            className.includes("grid-view")
              ? "h-[700px] md:h-[700px]"
              : "h-[160px]  sm:h-[220px] md:h-[300px]"
          } w-full overflow-hidden rounded-lg bg-white`}
        >
          <Image
            src={imageSrc}
            alt={name}
            fill
            className="object-contain transition-transform duration-300 group-hover:scale-105"
          />

          {/* Discount Badge - Top Left */}
          {discount && (
            <div className="absolute top-2 left-2">
              <span className="bg-blue rounded px-2 py-1 text-xs font-bold text-white">
                {discount}
              </span>
            </div>
          )}

          {/* Wishlist Button - Top Right */}
          <div className="absolute top-2 right-2">
            <div
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              <WishlistButton
                variant_id={variantId || id.toString()}
                product={{
                  name,
                  price,
                  image: imageSrc,
                  size,
                  color,
                  stock,
                }}
                size="sm"
                variant="ghost"
                className="rounded-full bg-white/80 p-2 hover:bg-white"
              />
            </div>
          </div>
        </div>
      </Link>

      {/* Product Info - Mobile vs Desktop */}
      <div className="md:hidden">
        <MobileLayout />
      </div>

      <div className="hidden md:block">
        <DesktopLayout />
      </div>
    </div>
  );
}
