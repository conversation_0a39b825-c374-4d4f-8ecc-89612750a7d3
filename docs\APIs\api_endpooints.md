# Sofa Deal E-Commerce API Documentation

## Table of Contents

- [1. Authentication](./authentication.md)
- [2. Categories](./categories.md)
- [3. Products](./products.md)
- [4. Product Tags](./product-tags.md)
- [5. Discounts](./discounts.md)
- [6. Wishlist](./wishlist.md)
- [7. Shopping Cart](./shopping-cart.md)
- [8. Cart](./cart.md)
- [9. Orders](./orders.md)
- [10. Users](./users.md)

## Introduction

Welcome to the Sofa Deal E-Commerce API documentation. This API provides a comprehensive set of endpoints to manage products, categories, product tags, user accounts, shopping carts, orders, and more for the Sofa Deal platform.

Each section in the Table of Contents links to a detailed breakdown of the API endpoints for that specific module. Please refer to the individual module documentation for request/response formats, authentication requirements, and usage examples.
