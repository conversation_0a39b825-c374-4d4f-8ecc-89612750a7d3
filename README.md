# Sofa Deal

A modern furniture shopping application built with Next.js, Tailwind CSS, Shadcn UI components, and Redux Toolkit for state management.

## Technologies Used

- **Next.js 14** - React framework with App Router
- **TypeScript** - For type safety
- **Tailwind CSS** - For styling
- **Shadcn UI** - For beautiful, accessible UI components
- **Redux Toolkit** - For state management

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://https://sopa-deal-production.up.railway.app:3000](http://https://sopa-deal-production.up.railway.app:3000) with your browser to see the result.

## Features

- Modern UI with Shadcn components
- Responsive design works on mobile and desktop
- State management with Redux Toolkit
- TypeScript for type safety

## Project Structure

- `src/app/*` - Next.js App Router files
- `src/components/*` - UI components including Shadcn UI components
- `src/lib/store/*` - Redux store configuration and slices
- `src/lib/providers/*` - Provider components

## Shadcn UI Components

This project uses a variety of components from Shadcn UI, including:

- Button
- Card
- Dialog
- Form
- Input
- Toast
- Alert
- Dropdown Menu
- Avatar
- Sheet
- Tabs

More components can be added using the Shadcn CLI:

```bash
npx shadcn@latest add [component-name]
```

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
