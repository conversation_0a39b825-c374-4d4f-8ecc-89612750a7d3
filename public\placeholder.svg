<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="400" height="300" fill="#f8f9fa"/>
  
  <!-- Border -->
  <rect x="1" y="1" width="398" height="298" stroke="#e9ecef" stroke-width="2" fill="none"/>
  
  <!-- Image icon -->
  <g transform="translate(150, 100)">
    <!-- Mountain/landscape icon -->
    <path d="M20 80 L40 40 L60 60 L80 20 L100 80 Z" fill="#dee2e6"/>
    <circle cx="30" cy="30" r="8" fill="#dee2e6"/>
    
    <!-- Camera icon overlay -->
    <g transform="translate(30, 45)">
      <rect x="0" y="8" width="40" height="28" rx="4" fill="#6c757d"/>
      <rect x="4" y="4" width="8" height="4" rx="2" fill="#6c757d"/>
      <circle cx="20" cy="22" r="8" fill="#f8f9fa"/>
      <circle cx="20" cy="22" r="5" fill="#6c757d"/>
    </g>
  </g>
  
  <!-- Text -->
  <text x="200" y="180" text-anchor="middle" fill="#6c757d" font-family="system-ui, -apple-system, sans-serif" font-size="14" font-weight="500">
    No Image Available
  </text>
  
  <!-- Subtle pattern -->
  <defs>
    <pattern id="dots" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="2" cy="2" r="1" fill="#f1f3f4" opacity="0.5"/>
    </pattern>
  </defs>
  <rect width="400" height="300" fill="url(#dots)"/>
</svg> 