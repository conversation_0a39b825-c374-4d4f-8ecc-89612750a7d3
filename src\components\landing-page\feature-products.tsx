"use client";

import { useFeaturedProducts } from "@/hooks/use-products";
import { ProductCard } from "@/components/product-card";
import { CountdownTimer } from "../count-down-timer";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { useState, useEffect } from "react";

const FeaturedProducts = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);

  const {
    data: featuredProducts,
    isLoading,
    error,
  } = useFeaturedProducts({
    limit: 6,
    includeCategory: true,
  });

  // Handle responsive detection
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);

    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  // Error state
  if (error) {
    return (
      <div className="py-10 md:py-16">
        <div className="px-[32px]">
          <div className="mb-8 md:mb-10">
            <h1 className="text-4xl lg:text-[85px]">SALES ENDS SOON</h1>
            <CountdownTimer />
          </div>

          <div className="py-8 text-center">
            <p className="font-open-sans text-red-600">
              Failed to load featured products. Please try again later.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Loading state
  if (isLoading || !featuredProducts || featuredProducts.length === 0) {
    return (
      <div className="py-10 md:py-16">
        <div className="px-[32px]">
          <div className="mb-8 md:mb-10">
            <h1 className="text-4xl lg:text-[85px]">SALES ENDS SOON</h1>
            <CountdownTimer />
          </div>

          {/* Mobile Loading - 2x2 grid */}
          <div className="grid grid-cols-2 gap-4 md:hidden">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="h-96 animate-pulse rounded-lg bg-gray-100"
              ></div>
            ))}
          </div>

          {/* Desktop Loading - 3 columns */}
          <div className="hidden grid-cols-3 gap-6 md:grid md:gap-8">
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className="h-96 animate-pulse rounded-lg bg-gray-100"
              ></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Calculate pagination for desktop only
  const itemsPerPageDesktop = 3;
  const totalPagesDesktop = Math.ceil(
    featuredProducts.length / itemsPerPageDesktop
  );

  // Slider navigation functions for desktop only
  // const nextSlide = () => {
  //   setCurrentIndex((prev) => (prev + 1) % totalPagesDesktop);
  // };

  // const prevSlide = () => {
  //   setCurrentIndex(
  //     (prev) => (prev - 1 + totalPagesDesktop) % totalPagesDesktop
  //   );
  // };

  // Get visible products for desktop (mobile shows first 4)
  const getVisibleDesktopProducts = () => {
    if (featuredProducts.length === 0) return [];

    const startIndex = currentIndex * itemsPerPageDesktop;
    return featuredProducts.slice(startIndex, startIndex + itemsPerPageDesktop);
  };

  // Get first 4 products for mobile 2x2 grid
  const getMobileProducts = () => {
    if (featuredProducts.length === 0) return [];
    return featuredProducts.slice(0, 4);
  };

  // Helper function to process product data
  const processProduct = (product: ,) => {
    const currentPrice = product.default_variant?.price || product.base_price;
    const hasDiscount = currentPrice < product.base_price;

    const getValidImageUrl = (imageUrl?: string) => {
      try {
        if (imageUrl && typeof imageUrl === "string") {
          if (imageUrl.startsWith("http") || imageUrl.startsWith("/")) {
            return imageUrl;
          }
        }
        return "/hero-img.png";
      } catch (error) {
        console.warn("Error processing product image:", error);
        return "/hero-img.png";
      }
    };

    const productImage = getValidImageUrl(product.main_image?.url);

    let discountPercentage = "";
    if (hasDiscount) {
      const percentage = Math.round(
        ((product.base_price - currentPrice) / product.base_price) * 100
      );
      discountPercentage = `${percentage}% off`;
    }

    return {
      ...product,
      currentPrice,
      hasDiscount,
      productImage,
      discountPercentage,
    };
  };

  return (
    <div className="py-10 md:py-16">
      <div className="px-2 md:px-[32px]">
        {/* Header */}
        <div className="mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between md:mb-10">
          <div className="w-full flex items-center justify-center md:justify-between">
            <h1 className="text-3xl lg:text-[85px]">SALES ENDS SOON</h1>
            <span className="ml-4 hidden lg:flex">
              <CountdownTimer />
            </span>
          </div>
        </div>

        {/* Mobile Countdown Timer */}
        {/* <div className="mb-6 block lg:hidden">
          <CountdownTimer />
        </div> */}

        {/* Products Container */}
        <div className="relative">
          {/* Mobile: Simple 2x2 Grid (no slider) */}
          <div className="md:hidden">
            <div className="grid grid-cols-2 gap-4">
              {getMobileProducts().map((product, index) => {
                const processedProduct = processProduct(product, index);

                return (
                  <ProductCard
                    variant={index % 2 === 0 ? "layout1" : "layout2"}
                    key={product.id}
                    id={product.id}
                    name={product.name || "SUNSET TURKISH SOFA"}
                    price={processedProduct.currentPrice}
                    originalPrice={
                      processedProduct.hasDiscount
                        ? product.base_price
                        : Math.round(processedProduct.currentPrice * 1.25)
                    }
                    imageSrc={processedProduct.productImage}
                    rating={4.9}
                    discount={processedProduct.discountPercentage || "15% off"}
                    deliveryInfo="3 To 4 Days Delivery"
                    paymentOption={{
                      service: "Klarna",
                      installments: 3,
                      amount:
                        Math.round((processedProduct.currentPrice / 3) * 100) /
                        100,
                    }}
                    isSale={processedProduct.hasDiscount}
                    variantId={product.default_variant?.id}
                    size={product.default_variant?.size}
                    color={product.default_variant?.color}
                    stock={product.default_variant?.stock}
                  />
                );
              })}
            </div>
          </div>

          {/* Desktop: 3 column grid with slider */}
          <div className="hidden md:block">
            <div className="grid grid-cols-3 gap-6 md:gap-8">
              {getVisibleDesktopProducts().map((product, index) => {
                const processedProduct = processProduct(product, index);

                return (
                  <ProductCard
                    variant={index % 2 === 0 ? "layout1" : "layout2"}
                    key={`${product.id}-${currentIndex}-${index}`}
                    id={product.id}
                    name={product.name || "SUNSET TURKISH SOFA"}
                    price={processedProduct.currentPrice}
                    originalPrice={
                      processedProduct.hasDiscount
                        ? product.base_price
                        : Math.round(processedProduct.currentPrice * 1.25)
                    }
                    imageSrc={processedProduct.productImage}
                    rating={4.9}
                    discount={processedProduct.discountPercentage || "15% off"}
                    deliveryInfo="3 To 4 Days Delivery"
                    paymentOption={{
                      service: "Klarna",
                      installments: 3,
                      amount:
                        Math.round((processedProduct.currentPrice / 3) * 100) /
                        100,
                    }}
                    isSale={processedProduct.hasDiscount}
                    variantId={product.default_variant?.id}
                    size={product.default_variant?.size}
                    color={product.default_variant?.color}
                    stock={product.default_variant?.stock}
                  />
                );
              })}
            </div>

            {/* Desktop Navigation Dots Only */}
            {/* {totalPagesDesktop > 1 && (
              <div className="mt-6 flex justify-center gap-2">
                {Array.from({ length: totalPagesDesktop }).map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`h-2 rounded-full transition-all ${
                      index === currentIndex ? "bg-blue w-6" : "w-2 bg-gray-300"
                    }`}
                  />
                ))}
              </div>
            )} */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeaturedProducts;
